{% extends 'ecom/customer_base.html' %}
{% load static %}
{% block content %}



  <style media="screen">
    body,
    html {
      height: 100%;
    }

    .d-flex {
      display: -webkit-box;
      display: -ms-flexbox;
      display: flex;
    }

    .align-center {
      -webkit-box-align: center;
      -ms-flex-align: center;
      align-items: center;
    }

    .flex-centerY-centerX {
      justify-content: center;
      -webkit-box-pack: center;
      -ms-flex-pack: center;
      justify-content: center;
      -webkit-box-align: center;
      -ms-flex-align: center;
      align-items: center;
    }

    body {
      background: linear-gradient(135deg, #1a237e 0%, #3949ab 100%);
      min-height: 100vh;
    }

    .page-wrapper {
      height: 100%;
      display: table;
    }

    .page-wrapper .page-inner {
      display: table-cell;
      vertical-align: middle;
    }

    .el-wrapper {
      width: 360px;
      padding: 15px;
      margin: 15px auto;
      background-color: #ffffff;
      border-radius: 12px;
      box-shadow: 0 8px 32px rgba(26, 35, 126, 0.15);
      transition: transform 0.3s ease, box-shadow 0.3s ease;
    }

    .el-wrapper:hover {
      transform: translateY(-5px);
      box-shadow: 0 16px 48px rgba(26, 35, 126, 0.25);
    }

    @media (max-width: 991px) {
      .el-wrapper {
        width: 345px;
      }
    }

    @media (max-width: 767px) {
      .el-wrapper {
        width: 290px;
        margin: 30px auto;
      }
    }

    .el-wrapper:hover .h-bg {
      left: 0px;
    }

    .el-wrapper:hover .price {
      left: 20px;
      -webkit-transform: translateY(-50%);
      -ms-transform: translateY(-50%);
      -o-transform: translateY(-50%);
      transform: translateY(-50%);
      
    }

    .el-wrapper:hover .add-to-cart {
      left: 50%;
    }

    .el-wrapper:hover .img {
      webkit-filter: blur(7px);
      -o-filter: blur(7px);
      -ms-filter: blur(7px);
      filter: blur(7px);
      filter: progid:DXImageTransform.Microsoft.Blur(pixelradius='7', shadowopacity='0.0');
      opacity: 0.4;
    }

    .el-wrapper:hover .info-inner {
      bottom: 155px;
    }

    .el-wrapper:hover .a-size {
      -webkit-transition-delay: 300ms;
      -o-transition-delay: 300ms;
      transition-delay: 300ms;
      bottom: 50px;
      opacity: 1;
    }

    .el-wrapper .box-down {
      width: 100%;
      height: 60px;
      position: relative;
      overflow: hidden;
      /*background: linear-gradient(135deg, #00acc1 0%, #00bcd4 100%);*/
      background: linear-gradient(135deg,rgb(93, 108, 206) 0%,rgb(122, 134, 208) 100%);
      border-radius: 0 0 12px 12px;
    }

    .el-wrapper .box-up {
      width: 100%;
      height: 300px;
      position: relative;
      overflow: hidden;
      text-align: center;
    }

    .el-wrapper .img {
      padding: 20px 0;
      -webkit-transition: all 800ms cubic-bezier(0, 0, 0.18, 1);
      -moz-transition: all 800ms cubic-bezier(0, 0, 0.18, 1);
      -o-transition: all 800ms cubic-bezier(0, 0, 0.18, 1);
      transition: all 800ms cubic-bezier(0, 0, 0.18, 1);
      /* ease-out */
      -webkit-transition-timing-function: cubic-bezier(0, 0, 0.18, 1);
      -moz-transition-timing-function: cubic-bezier(0, 0, 0.18, 1);
      -o-transition-timing-function: cubic-bezier(0, 0, 0.18, 1);
      transition-timing-function: cubic-bezier(0, 0, 0.18, 1);
      /* ease-out */
    }

    .h-bg {
      -webkit-transition: all 800ms cubic-bezier(0, 0, 0.18, 1);
      -moz-transition: all 800ms cubic-bezier(0, 0, 0.18, 1);
      -o-transition: all 800ms cubic-bezier(0, 0, 0.18, 1);
      transition: all 800ms cubic-bezier(0, 0, 0.18, 1);
      /* ease-out */
      -webkit-transition-timing-function: cubic-bezier(0, 0, 0.18, 1);
      -moz-transition-timing-function: cubic-bezier(0, 0, 0.18, 1);
      -o-transition-timing-function: cubic-bezier(0, 0, 0.18, 1);
      transition-timing-function: cubic-bezier(0, 0, 0.18, 1);
      /* ease-out */
      width: 660px;
      height: 100%;
      /*background-color: #3f96cd;*/
      position: absolute;
      left: -659px;
    }

    .h-bg .h-bg-inner {
      width: 50%;
      height: 100%;
      background-color: #ffa78b;
    }

    .info-inner {
      -webkit-transition: all 400ms cubic-bezier(0, 0, 0.18, 1);
      -moz-transition: all 400ms cubic-bezier(0, 0, 0.18, 1);
      -o-transition: all 400ms cubic-bezier(0, 0, 0.18, 1);
      transition: all 400ms cubic-bezier(0, 0, 0.18, 1);
      /* ease-out */
      -webkit-transition-timing-function: cubic-bezier(0, 0, 0.18, 1);
      -moz-transition-timing-function: cubic-bezier(0, 0, 0.18, 1);
      -o-transition-timing-function: cubic-bezier(0, 0, 0.18, 1);
      transition-timing-function: cubic-bezier(0, 0, 0.18, 1);
      /* ease-out */
      position: absolute;
      width: 100%;
      bottom: 25px;
    }

    .info-inner .p-name,
    .info-inner .p-company {
      display: block;
    }

    .info-inner .p-name {
      font-family: 'PT Sans', sans-serif;
      font-size: 18px;
      color: #252525;
    }

    .info-inner .p-company {
      font-family: 'Lato', sans-serif;
      font-size: 12px;
      text-transform: uppercase;
      color: #FFE11B;
    }

    .a-size {
      -webkit-transition: all 300ms cubic-bezier(0, 0, 0.18, 1);
      -moz-transition: all 300ms cubic-bezier(0, 0, 0.18, 1);
      -o-transition: all 300ms cubic-bezier(0, 0, 0.18, 1);
      transition: all 300ms cubic-bezier(0, 0, 0.18, 1);
      /* ease-out */
      -webkit-transition-timing-function: cubic-bezier(0, 0, 0.18, 1);
      -moz-transition-timing-function: cubic-bezier(0, 0, 0.18, 1);
      -o-transition-timing-function: cubic-bezier(0, 0, 0.18, 1);
      transition-timing-function: cubic-bezier(0, 0, 0.18, 1);
      /* ease-out */
      position: absolute;
      width: 100%;
      bottom: -20px;
      font-family: 'PT Sans', sans-serif;
      color: #828282;
      opacity: 0;
    }

    .a-size .size {
      color: #252525;
    }

    .cart {
      display: block;
      position: absolute;
      width: 100%;
      height: 100%;
      top: 0;
      left: 0;
      font-family: 'Lato', sans-serif;
      font-weight: 700;
    }

    .cart .price {
      -webkit-transition: all 600ms cubic-bezier(0, 0, 0.18, 1);
      -moz-transition: all 600ms cubic-bezier(0, 0, 0.18, 1);
      -o-transition: all 600ms cubic-bezier(0, 0, 0.18, 1);
      transition: all 600ms cubic-bezier(0, 0, 0.18, 1);
      /* ease-out */
      -webkit-transition-timing-function: cubic-bezier(0, 0, 0.18, 1);
      -moz-transition-timing-function: cubic-bezier(0, 0, 0.18, 1);
      -o-transition-timing-function: cubic-bezier(0, 0, 0.18, 1);
      transition-timing-function: cubic-bezier(0, 0, 0.18, 1);
      /* ease-out */
      -webkit-transition-delay: 100ms;
      -o-transition-delay: 100ms;
      transition-delay: 100ms;
      display: block;
      position: absolute;
      top: 50%;
      left: 50%;
      -webkit-transform: translate(-50%, -50%);
      -ms-transform: translate(-50%, -50%);
      -o-transform: translate(-50%, -50%);
      transform: translate(-50%, -50%);
      font-size: 16px;
      color: #252525;
    }

    .cart .add-to-cart {
      -webkit-transition: all 600ms cubic-bezier(0, 0, 0.18, 1);
      -moz-transition: all 600ms cubic-bezier(0, 0, 0.18, 1);
      -o-transition: all 600ms cubic-bezier(0, 0, 0.18, 1);
      transition: all 600ms cubic-bezier(0, 0, 0.18, 1);
      /* ease-out */
      -webkit-transition-timing-function: cubic-bezier(0, 0, 0.18, 1);
      -moz-transition-timing-function: cubic-bezier(0, 0, 0.18, 1);
      -o-transition-timing-function: cubic-bezier(0, 0, 0.18, 1);
      transition-timing-function: cubic-bezier(0, 0, 0.18, 1);
      /* ease-out */
      -webkit-transition-delay: 100ms;
      -o-transition-delay: 100ms;
      transition-delay: 100ms;
      display: block;
      position: absolute;
      top: 50%;
      left: 110%;
      -webkit-transform: translate(-50%, -50%);
      -ms-transform: translate(-50%, -50%);
      -o-transform: translate(-50%, -50%);
      transform: translate(-50%, -50%);
    }

    .cart .add-to-cart .txt {
      font-size: 12px;
      color: #1a237e;
      letter-spacing: 0.045em;
      text-transform: uppercase;
      white-space: nowrap;
    }
    * {
      box-sizing: border-box;
    }

    /* Create three equal columns that floats next to each other */
    .column {
      float: left;
      width: 33.33%;
      padding: 10px;
      height: 300px; /* Should be removed. Only for demonstration */
    }

    /* Clear floats after the columns */
    .row:after {
      content: "";
      display: table;
      clear: both;
    }

    /* Responsive layout - makes the three columns stack on top of each other instead of next to each other */
    @media screen and (max-width: 600px) {
      .column {
        width: 100%;
      }
    }
  </style>




  {%if products%}
  <h3 style="text-align:center; color:yellow;">{{word}}</h3>
  <br>
  <!-- products data start -->
  <div class="row">

      {% for p in products %}
      <!-- card1 -->
      <div class="column">
      <div class="container page-wrapper" style="width: 450px;">
        <div class="page-inner">
          <div class="row">
            <div class="el-wrapper">
              <div class="box-up">
                {% if p.get_image_url %}
                <img class="img" src="{{ p.get_image_url }}" alt="product pic" height="300px" width="300px">
                {% else %}
                <img class="img" src="{% static 'images/default-product.png' %}" alt="product pic" height="300px" width="300px">
                {% endif %}
                <div class="img-info">
                  <div class="info-inner">
                    <span style="background-color:#08050a; color:yellow;" class="p-company">{{p.name}}</span>

                  </div>
                  <div class="a-size">{{p.description}}</div>
                </div>
              </div>

              <div class="box-down">
                <div class="h-bg">
                  <div class="h-bg-inner"></div>
                </div>

                <a class="cart" href="{% url 'add-to-cart' p.id  %}">
                  <span class="price">₱{{p.price}}</span>
                  <span class="add-to-cart">
                    <span class="txt">Add in cart</span>
                  </span>
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
      </div>
<!-- card1 end -->
{% if forloop.counter|divisibleby:"3" %}
    </div>
    <div class="row">
          <br><br><br><br><br>
{% endif %}

  {% endfor %}
</div>
<!-- products data enddddddddddddddddddddddddddddddddddddddddddddddddddddddddddd -->

<!-- Pagination Controls -->
{% if products.has_other_pages %}
<div class="pagination-container" style="text-align: center; margin: 40px 0;">
    <nav aria-label="Product pagination">
        <ul class="pagination justify-content-center" style="display: inline-flex; list-style: none; padding: 0; margin: 0;">

            {% if products.has_previous %}
                <li class="page-item">
                    <a class="page-link" href="?{% if query %}query={{ query }}&{% endif %}page=1" style="color: #ffffff; text-decoration: none; padding: 10px 15px; margin: 0 3px; border: 2px solid #ffffff; border-radius: 8px; background-color: transparent; transition: all 0.3s ease;" onmouseover="this.style.backgroundColor='#ffffff'; this.style.color='#000000';" onmouseout="this.style.backgroundColor='transparent'; this.style.color='#ffffff';">First</a>
                </li>
                <li class="page-item">
                    <a class="page-link" href="?{% if query %}query={{ query }}&{% endif %}page={{ products.previous_page_number }}" style="color: #ffffff; text-decoration: none; padding: 10px 15px; margin: 0 3px; border: 2px solid #ffffff; border-radius: 8px; background-color: transparent; transition: all 0.3s ease;" onmouseover="this.style.backgroundColor='#ffffff'; this.style.color='#000000';" onmouseout="this.style.backgroundColor='transparent'; this.style.color='#ffffff';">Previous</a>
                </li>
            {% endif %}

            {% for num in products.paginator.page_range %}
                {% if products.number == num %}
                    <li class="page-item active">
                        <span class="page-link" style="color: #000000; padding: 10px 15px; margin: 0 3px; border: 2px solid #ffffff; border-radius: 8px; background-color: #ffffff; font-weight: bold;">{{ num }}</span>
                    </li>
                {% elif num > products.number|add:'-3' and num < products.number|add:'3' %}
                    <li class="page-item">
                        <a class="page-link" href="?{% if query %}query={{ query }}&{% endif %}page={{ num }}" style="color: #ffffff; text-decoration: none; padding: 10px 15px; margin: 0 3px; border: 2px solid #ffffff; border-radius: 8px; background-color: transparent; transition: all 0.3s ease;" onmouseover="this.style.backgroundColor='#ffffff'; this.style.color='#000000';" onmouseout="this.style.backgroundColor='transparent'; this.style.color='#ffffff';">{{ num }}</a>
                    </li>
                {% endif %}
            {% endfor %}

            {% if products.has_next %}
                <li class="page-item">
                    <a class="page-link" href="?{% if query %}query={{ query }}&{% endif %}page={{ products.next_page_number }}" style="color: #ffffff; text-decoration: none; padding: 10px 15px; margin: 0 3px; border: 2px solid #ffffff; border-radius: 8px; background-color: transparent; transition: all 0.3s ease;" onmouseover="this.style.backgroundColor='#ffffff'; this.style.color='#000000';" onmouseout="this.style.backgroundColor='transparent'; this.style.color='#ffffff';">Next</a>
                </li>
                <li class="page-item">
                    <a class="page-link" href="?{% if query %}query={{ query }}&{% endif %}page={{ products.paginator.num_pages }}" style="color: #ffffff; text-decoration: none; padding: 10px 15px; margin: 0 3px; border: 2px solid #ffffff; border-radius: 8px; background-color: transparent; transition: all 0.3s ease;" onmouseover="this.style.backgroundColor='#ffffff'; this.style.color='#000000';" onmouseout="this.style.backgroundColor='transparent'; this.style.color='#ffffff';">Last</a>
                </li>
            {% endif %}

        </ul>
    </nav>

    <div style="margin-top: 20px; color: #ffffff; font-size: 16px; font-weight: 500; text-shadow: 1px 1px 2px rgba(0,0,0,0.5);">
        Page {{ products.number }} of {{ products.paginator.num_pages }}
        ({{ products.paginator.count }} total products)
    </div>
</div>
{% endif %}

{%else%}
<h3 style="text-align:center; color:yellow;">No Search Found</h3>
{%endif%}

<script>
    {% comment %} {%if messages %}
    {%for message in messages%}
    alert('{{message}}');
    {%endfor%}
    {%endif%} {% endcomment %}
</script>
<br><br><br>
<br><br><br><br><br><br><br><br><br><br><br><br>
{% endblock content %}
