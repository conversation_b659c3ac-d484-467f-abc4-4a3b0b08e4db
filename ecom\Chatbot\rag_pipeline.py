# # your_app/rag_pipeline.py

# import os
# from langchain_community.document_loaders import TextLoader
# from langchain.text_splitter import RecursiveCharacterTextSplitter
# from langchain_community.embeddings import HuggingFaceEmbeddings
# from langchain_community.vectorstores import Chroma
# from langchain_community.llms import Ollama
# from langchain.prompts import PromptTemplate
# from langchain.schema.runnable import RunnablePassthrough
# from langchain.schema.output_parser import StrOutputParser
# from dotenv import load_dotenv

# # Load environment variables from .env file (optional)
# load_dotenv()

# # --- Configuration ---
# KNOWLEDGE_BASE_PATH = "C:\\Users\\<USER>\\OneDrive\\Desktop\\Online_Shopping_Project_Django_Development\\Chatbot\\knowledge_base.txt"
# VECTOR_STORE_PATH = "./chroma_db"
# EMBEDDING_MODEL_NAME = "sentence-transformers/all-MiniLM-L6-v2"
# OLLAMA_MODEL = "mistral:7b-instruct-q4_K_M"  # Your local Ollama model

# # --- Global Variables ---
# retriever = None

# def initialize_rag_pipeline():
#     """
#     Initializes the RAG pipeline by loading data, creating embeddings,
#     and setting up the retriever. This should be called once at application startup.
#     """
#     global retriever

#     # 1. Load Documents
#     loader = TextLoader(KNOWLEDGE_BASE_PATH , autodetect_encoding=True)
#     documents = loader.load()

#     # 2. Chunk Documents
#     text_splitter = RecursiveCharacterTextSplitter(chunk_size=1000, chunk_overlap=200)
#     chunked_documents = text_splitter.split_documents(documents)

#     # 3. Embeddings
#     embeddings = HuggingFaceEmbeddings(model_name=EMBEDDING_MODEL_NAME)

#     # 4. Create or Load Chroma Vector Store
#     if os.path.exists(VECTOR_STORE_PATH):
#         print("Loading existing vector store...")
#         vector_store = Chroma(persist_directory=VECTOR_STORE_PATH, embedding_function=embeddings)
#     else:
#         print("Creating new vector store...")
#         vector_store = Chroma.from_documents(
#             documents=chunked_documents,
#             embedding=embeddings,
#             persist_directory=VECTOR_STORE_PATH
#         )
#         vector_store.persist()
    
#     # 5. Retriever
#     retriever = vector_store.as_retriever(search_kwargs={"k": 3})
#     print("✅ RAG pipeline initialized successfully.")

# def get_chatbot_response(query: str) -> str:
#     """
#     Gets a response from the RAG pipeline for a given query.
#     """
#     global retriever
#     if retriever is None:
#         return "Error: RAG pipeline is not initialized. Please run initialize_rag_pipeline() first."

#     # 6. Prompt Template
#     template = """
#     Use the following pieces of context to answer the question at the end.
#     This context is from an e-commerce website's knowledge base.
#     If you don't know the answer from the provided context, just say you don't have information on that topic.
#     Do not make up an answer or use external knowledge.
#     Keep the answer concise and directly related to the e-commerce website.

#     Context: {context}

#     Question: {question}

#     Helpful Answer:
#     """
#     prompt = PromptTemplate(template=template, input_variables=["context", "question"])

#     # 7. Local Ollama LLM
#     llm = Ollama(
#         base_url="http://localhost:11434",
#         model=OLLAMA_MODEL,
#         temperature=0.5
#     )

#     # 8. RAG Chain
#     rag_chain = (
#         {"context": retriever, "question": RunnablePassthrough()}
#         | prompt
#         | llm
#         | StrOutputParser()
#     )

#     # 9. Run Query
#     return rag_chain.invoke(query)


# your_app/rag_pipeline.py

import os
# from dotenv import load_dotenv
from langchain_community.document_loaders import TextLoader
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain_huggingface import HuggingFaceEmbeddings
from langchain_chroma import Chroma
from langchain_ollama import OllamaLLM
from langchain.prompts import PromptTemplate
from langchain.schema.runnable import RunnablePassthrough
from langchain.schema.output_parser import StrOutputParser

# Load environment variables from .env file
# load_dotenv()

# --- Configuration ---
KNOWLEDGE_BASE_PATH = "ecom/Chatbot/knowledge_base.txt"
# KNOWLEDGE_BASE_PATH = "./knowledge_base.txt"
VECTOR_STORE_PATH = "ecom/Chatbot/chroma_db_utf8"
EMBEDDING_MODEL_NAME = "sentence-transformers/all-MiniLM-L6-v2"
OLLAMA_MODEL = "mistral:7b-instruct-q4_K_M"
# OLLAMA_MODEL = "llama3.2:3b"

# --- Global Variables ---
retriever = None
_rag_initialized = False

def initialize_rag_pipeline():
    """
    Initializes the RAG pipeline by loading data, creating embeddings,
    and setting up the retriever. This should be called once at application startup.
    """
    global retriever, _rag_initialized

    # Check if already initialized
    if _rag_initialized and retriever is not None:
        return  # Silent return if already initialized

    try:
        print("🚀 Initializing RAG pipeline...")

        # 1. Load Documents from the knowledge base file with UTF-8 encoding
        loader = TextLoader(KNOWLEDGE_BASE_PATH, encoding='utf-8')
        documents = loader.load()

        # 2. Chunk Documents
        text_splitter = RecursiveCharacterTextSplitter(chunk_size=1000, chunk_overlap=200)
        chunked_documents = text_splitter.split_documents(documents)

        # 3. Initialize Embeddings Model with offline fallback
        try:
            print("🔄 Trying to load sentence transformer model...")
            embeddings = HuggingFaceEmbeddings(
                model_name=EMBEDDING_MODEL_NAME,
                model_kwargs={'device': 'cpu'},  # Force CPU to avoid GPU issues
                encode_kwargs={'normalize_embeddings': True}
            )
            print("✅ Sentence transformer model loaded successfully!")
        except Exception as e:
            print(f"⚠️ Failed to load sentence transformer: {e}")
            print("🔄 Falling back to TF-IDF based embeddings...")
            # Use a simple TF-IDF based approach as fallback
            from sklearn.feature_extraction.text import TfidfVectorizer
            from langchain.embeddings.base import Embeddings
            import numpy as np

            class TFIDFEmbeddings(Embeddings):
                def __init__(self):
                    self.vectorizer = TfidfVectorizer(max_features=384, stop_words='english')
                    self.fitted = False

                def embed_documents(self, texts):
                    if not self.fitted:
                        self.vectorizer.fit(texts)
                        self.fitted = True
                    vectors = self.vectorizer.transform(texts).toarray()
                    return vectors.tolist()

                def embed_query(self, text):
                    if not self.fitted:
                        return [0.0] * 384
                    vector = self.vectorizer.transform([text]).toarray()[0]
                    return vector.tolist()

            embeddings = TFIDFEmbeddings()
            print("✅ TF-IDF embeddings initialized successfully!")

    # 4. Create or Load Vector Store
    # if os.path.exists(VECTOR_STORE_PATH):
    #     print("📂 Loading existing vector store...")
    #     vector_store = Chroma(persist_directory=VECTOR_STORE_PATH, embedding_function=embeddings)
    # else:
    #     print("🔧 Creating new vector store...")
    #     vector_store = Chroma.from_documents(
    #         documents=chunked_documents,
    #         embedding=embeddings,
    #         persist_directory=VECTOR_STORE_PATH
    #     )
        print("🔧 Creating new vector store...")
        vector_store = Chroma.from_documents(
            documents=chunked_documents,
            embedding=embeddings,
            persist_directory=VECTOR_STORE_PATH
        )

        # 5. Create a Retriever with optimized search parameters
        retriever = vector_store.as_retriever(
            search_type="similarity",
            search_kwargs={"k": 6}  # Retrieve more documents for better context
        )
        _rag_initialized = True
        print("✅ RAG pipeline initialized successfully!")

    except Exception as e:
        print(f"❌ Chatbot initialization Exception: {e}")
        print("🔄 Attempting simplified initialization...")

        # Fallback: Create a simple keyword-based retriever
        try:
            from langchain.schema import Document

            # Create simple documents from knowledge base
            with open(KNOWLEDGE_BASE_PATH, 'r', encoding='utf-8') as f:
                content = f.read()

            # Simple keyword-based retriever
            class SimpleKeywordRetriever:
                def __init__(self, content):
                    self.content = content.lower()
                    self.sections = content.split('\n\n')

                def get_relevant_documents(self, query):
                    query_lower = query.lower()
                    relevant_sections = []

                    for section in self.sections:
                        if any(word in section.lower() for word in query_lower.split()):
                            relevant_sections.append(Document(page_content=section))

                    return relevant_sections[:6] if relevant_sections else [Document(page_content=self.content[:1000])]

            retriever = SimpleKeywordRetriever(content)
            _rag_initialized = True
            print("✅ Fallback keyword-based retriever initialized!")

        except Exception as fallback_error:
            print(f"❌ Fallback initialization also failed: {fallback_error}")
            print("⚠️ Chatbot will use basic responses only.")

def get_chatbot_response(query: str) -> str:
    """
    Gets a response from the RAG pipeline for a given query.
    """
    global retriever
    if retriever is None:
        return "Error: RAG pipeline is not initialized. Please run initialize_rag_pipeline() first."


    # Step 1: Improved context retrieval with query enhancement
    # Enhance query for better matching
    enhanced_query = query
    query_lower = query.lower()

    # Add relevant keywords to improve search
    if "free shipping" in query_lower or "shipping" in query_lower:
        enhanced_query = f"{query} shipping policy delivery cost free orders"
    elif "return" in query_lower or "refund" in query_lower:
        enhanced_query = f"{query} return policy refund exchange"
    elif "payment" in query_lower:
        enhanced_query = f"{query} payment methods cards UPI COD"

    # Retrieve context using enhanced query
    retrieved_docs = retriever.get_relevant_documents(enhanced_query)
    context_text = "\n\n".join([doc.page_content for doc in retrieved_docs])

    # Fix currency encoding issues
    context_text = context_text.replace('‚Çπ', '₹').replace('â‚¹', '₹')

    # Print the retrieved context for debugging
    # print("\n--- Retrieved Context ---")
    # print(context_text)
    # print("--- End of Context ---\n")
    
    
    # 6. Strict Context-Only Prompt Template
    template = """You are an e-commerce assistant. Answer customer questions using ONLY the information provided in the context below.

STRICT RULES:
- Answer ONLY what is asked using information from the context
- All prices MUST be written in rupees format as: ₹ {{amount with comma separators}}, e.g., ₹ 5,000 or ₹ 12,34,567
- If the exact product/information isn't in the context, say "I don't have information about that"
- NEVER mention discounts, offers, or promotions unless they are explicitly stated in the context
- NEVER add extra information not found in the context
- Do NOT make up any information
- Be direct and factual
- Provide a clean, direct response without any formatting markers

Context: {context}

Question: {question}

Response:"""


    prompt = PromptTemplate(template=template, input_variables=["context", "question"])

    # 7. Local LLM via Ollama
    llm = OllamaLLM(model=OLLAMA_MODEL, base_url="http://localhost:11434")

    # 8. Format prompt with context and question
    formatted_prompt = prompt.format(context=context_text, question=query)

    # 9. Get response from LLM
    response = llm.invoke(formatted_prompt)

    # 10. Clean and format the response
    if isinstance(response, str):
        # Fix currency encoding issues
        response = response.replace('‚Çπ', '₹').replace('â‚¹', '₹').replace('Rs.', '₹')

        # Remove unwanted formatting and prefixes
        response = response.strip()

        # Remove markdown code blocks
        if response.startswith('```') and response.endswith('```'):
            response = response[3:-3].strip()

        # Remove common prefixes that might appear
        prefixes_to_remove = ['Answer:', 'Response:', 'answer:', 'response:']
        for prefix in prefixes_to_remove:
            if response.startswith(prefix):
                response = response[len(prefix):].strip()

        # Remove any remaining markdown formatting
        response = response.replace('```', '').strip()

    return response