from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain_community.document_loaders import TextLoader

# Load the knowledge base file
loader = TextLoader('./knowledge_base.txt' , autodetect_encoding=True)
documents = loader.load()

# Initialize the text splitter
text_splitter = RecursiveCharacterTextSplitter(
    chunk_size=1000,  # The maximum size of each chunk (in characters)
    chunk_overlap=200, # The number of characters to overlap between chunks
    length_function=len
)

# Split the document into chunks
chunked_documents = text_splitter.split_documents(documents)

# # You can now inspect the chunks
# print(f"Original document has {len(documents.page_content)} characters.")
# print(f"Split into {len(chunked_documents)} chunks.")
# print(f"Example chunk: {chunked_documents.page_content}")

total_chars = sum(len(doc.page_content) for doc in documents)
print(f"Original document has {total_chars} characters.")
print(f"Split into {len(chunked_documents)} chunks.")
print(f"Example chunk:\n{chunked_documents[1].page_content}")
